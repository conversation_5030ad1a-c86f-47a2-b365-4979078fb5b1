#!/usr/bin/env node

/**
 * ATMA Test Runner
 * Script untuk menjalankan berb<PERSON>i jenis test dengan mudah
 */

const { spawn } = require('child_process');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
  console.log('\n' + '='.repeat(60));
  console.log(colorize(title, 'cyan'));
  console.log('='.repeat(60));
}

function printUsage() {
  console.log(colorize('\n🧪 ATMA Test Runner', 'bright'));
  console.log('\nUsage: node run-tests.js [command]');
  console.log('\nAvailable commands:');
  console.log(colorize('  full-flow', 'green') + '     - Run complete end-to-end test');
  console.log(colorize('  individual', 'green') + '    - Run individual component tests');
  console.log(colorize('  jest', 'green') + '         - Run all Jest tests');
  console.log(colorize('  load-test', 'green') + '     - Run load test with 500 concurrent users');
  console.log(colorize('  health', 'green') + '       - Check service health');
  console.log(colorize('  help', 'green') + '        - Show this help message');
  console.log('\nExamples:');
  console.log('  node run-tests.js full-flow');
  console.log('  node run-tests.js individual');
  console.log('  node run-tests.js jest');
  console.log('  node run-tests.js load-test');
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(colorize(`\n🚀 Running: ${command} ${args.join(' ')}`, 'blue'));
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(colorize(`✅ Command completed successfully`, 'green'));
        resolve(code);
      } else {
        console.log(colorize(`❌ Command failed with code ${code}`, 'red'));
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.log(colorize(`❌ Command error: ${error.message}`, 'red'));
      reject(error);
    });
  });
}

async function checkServiceHealth() {
  printHeader('🏥 Service Health Check');
  
  const services = [
    { name: 'Auth Service', url: 'http://localhost:3001/health/live' },
    { name: 'Assessment Service', url: 'http://localhost:3003/health/live' },
    { name: 'Archive Service', url: 'http://localhost:3002/health/live' },
    { name: 'Notification Service', url: 'http://localhost:3005/health' }
  ];

  const axios = require('axios');
  
  for (const service of services) {
    try {
      console.log(`🔍 Checking ${service.name}...`);
      const response = await axios.get(service.url, { timeout: 5000 });
      console.log(colorize(`✅ ${service.name}: ${response.data.status}`, 'green'));
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(colorize(`❌ ${service.name}: Service not running`, 'red'));
      } else {
        console.log(colorize(`❌ ${service.name}: ${error.message}`, 'red'));
      }
    }
  }
}

async function runFullFlowTest() {
  printHeader('🔄 Full Flow Integration Test');
  console.log('This test will run the complete end-to-end flow:');
  console.log('1. Register user');
  console.log('2. Login user');
  console.log('3. Submit assessment');
  console.log('4. Wait for processing');
  console.log('5. Verify results in archive');
  console.log('\n⏳ This may take 2-5 minutes...\n');
  
  await runCommand('node', ['full-flow-test.js']);
}

async function runIndividualTests() {
  printHeader('🧩 Individual Component Tests');
  console.log('Running Jest tests for individual components...\n');
  
  await runCommand('npx', ['jest', 'individual-tests.test.js', '--verbose']);
}

async function runJestTests() {
  printHeader('🧪 All Jest Tests');
  console.log('Running all Jest test suites...\n');

  await runCommand('npx', ['jest', '--verbose']);
}

async function runLoadTest() {
  printHeader('⚡ Load Test - 500 Concurrent Users');
  console.log('This test will simulate 500 users accessing the system simultaneously:');
  console.log('1. Register 500 users');
  console.log('2. Login all users');
  console.log('3. Submit assessments concurrently');
  console.log('4. Wait for processing');
  console.log('5. Verify results');
  console.log('\n⏳ This may take 10-20 minutes depending on system performance...\n');

  await runCommand('node', ['load-test.js']);
}

async function main() {
  const command = process.argv[2];

  if (!command || command === 'help') {
    printUsage();
    return;
  }

  try {
    switch (command) {
      case 'full-flow':
        await runFullFlowTest();
        break;
        
      case 'individual':
        await runIndividualTests();
        break;
        
      case 'jest':
        await runJestTests();
        break;

      case 'load-test':
        await runLoadTest();
        break;

      case 'health':
        await checkServiceHealth();
        break;
        
      default:
        console.log(colorize(`❌ Unknown command: ${command}`, 'red'));
        printUsage();
        process.exit(1);
    }
    
    console.log(colorize('\n🎉 All tests completed successfully!', 'green'));
    
  } catch (error) {
    console.log(colorize(`\n💥 Test execution failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log(colorize('\n\n⚠️ Test execution interrupted by user', 'yellow'));
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = { main };
