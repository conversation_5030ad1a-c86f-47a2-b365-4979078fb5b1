#!/usr/bin/env node

/**
 * ATMA Load Test - 500 Concurrent Users
 * Script untuk melakukan load testing dengan 500 user bersamaan
 */

const TestHelpers = require('./utils/test-helpers');
const pLimit = require('p-limit');
const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Load environment configuration
require('dotenv').config();

// Global test configuration
global.testConfig = {
  authServiceUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  assessmentServiceUrl: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003',
  archiveServiceUrl: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
  apiGatewayUrl: process.env.API_GATEWAY_URL || 'http://localhost:3000',
  notificationServiceUrl: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005',
  testPassword: process.env.TEST_PASSWORD || 'testpass123',
  serviceApiKey: process.env.SERVICE_API_KEY || 'test-service-key'
};

// Global helper functions
global.generateTestEmail = () => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `test-${timestamp}-${random}@example.com`;
};

global.sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

class LoadTestRunner {
  constructor() {
    this.testHelpers = new TestHelpers();
    this.results = {
      total: 0,
      successful: 0,
      failed: 0,
      errors: [],
      timings: [],
      startTime: null,
      endTime: null
    };
  }

  /**
   * Run single user test scenario
   */
  async runSingleUserTest(userIndex) {
    const startTime = Date.now();
    const testId = `user-${userIndex}`;
    
    try {
      console.log(colorize(`🚀 [${testId}] Starting test...`, 'blue'));
      
      // 1. Register user
      const testUser = await this.testHelpers.registerUser();
      console.log(colorize(`📝 [${testId}] User registered: ${testUser.email}`, 'green'));
      
      // 2. Login user
      const loginResult = await this.testHelpers.loginUser(testUser.email, testUser.password);
      const authToken = loginResult.token;
      console.log(colorize(`🔐 [${testId}] User logged in`, 'green'));
      
      // 3. Submit assessment
      const assessmentData = this.testHelpers.loadSampleAssessment();
      const submitResult = await this.testHelpers.submitAssessment(authToken, assessmentData);
      const jobId = submitResult.data.jobId;
      console.log(colorize(`📊 [${testId}] Assessment submitted: ${jobId}`, 'green'));
      
      // 4. Wait for completion (shorter timeout for load test)
      const completionResult = await this.testHelpers.waitForAssessmentCompletion(
        authToken, 
        jobId, 
        60000 // 60 seconds timeout
      );
      
      // 5. Verify results in archive
      await global.sleep(2000); // Wait for archive save
      const archiveResults = await this.testHelpers.getAnalysisResults(authToken);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(colorize(`✅ [${testId}] Test completed in ${duration}ms`, 'green'));
      
      return {
        success: true,
        userIndex,
        testId,
        email: testUser.email,
        jobId,
        duration,
        archetype: archiveResults.data.length > 0 ? archiveResults.data[0].persona_profile.archetype : 'unknown'
      };
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(colorize(`❌ [${testId}] Test failed: ${error.message}`, 'red'));
      
      return {
        success: false,
        userIndex,
        testId,
        error: error.message,
        duration
      };
    }
  }

  /**
   * Run load test with specified number of concurrent users
   */
  async runLoadTest(totalUsers = 500, concurrency = 50) {
    console.log(colorize('\n🧪 ATMA LOAD TEST - 500 CONCURRENT USERS', 'cyan'));
    console.log('='.repeat(80));
    console.log(colorize(`👥 Total Users: ${totalUsers}`, 'yellow'));
    console.log(colorize(`⚡ Concurrency: ${concurrency}`, 'yellow'));
    console.log(colorize(`🎯 Target: Full assessment flow per user`, 'yellow'));
    console.log('='.repeat(80));
    
    // Check service health first
    console.log(colorize('\n🏥 Checking service health...', 'blue'));
    try {
      await this.testHelpers.checkAllServicesHealth();
      console.log(colorize('✅ All services are healthy', 'green'));
    } catch (error) {
      console.log(colorize('❌ Some services are not healthy. Continuing anyway...', 'yellow'));
    }
    
    this.results.startTime = Date.now();
    this.results.total = totalUsers;
    
    // Create concurrency limiter
    const limit = pLimit(concurrency);
    
    // Create array of user test promises
    const userTests = Array.from({ length: totalUsers }, (_, index) => 
      limit(() => this.runSingleUserTest(index + 1))
    );
    
    console.log(colorize(`\n🚀 Starting ${totalUsers} user tests with ${concurrency} concurrent...`, 'blue'));
    
    // Progress tracking
    let completed = 0;
    const progressInterval = setInterval(() => {
      const progress = Math.round((completed / totalUsers) * 100);
      console.log(colorize(`⏳ Progress: ${completed}/${totalUsers} (${progress}%) - Success: ${this.results.successful}, Failed: ${this.results.failed}`, 'yellow'));
    }, 5000);
    
    // Execute all tests
    try {
      const results = await Promise.allSettled(userTests);
      
      clearInterval(progressInterval);
      this.results.endTime = Date.now();
      
      // Process results
      results.forEach((result, index) => {
        completed++;
        
        if (result.status === 'fulfilled' && result.value.success) {
          this.results.successful++;
          this.results.timings.push(result.value.duration);
        } else {
          this.results.failed++;
          const error = result.status === 'rejected' ? result.reason.message : result.value.error;
          this.results.errors.push({
            userIndex: index + 1,
            error: error
          });
        }
      });
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      clearInterval(progressInterval);
      console.log(colorize(`💥 Load test failed: ${error.message}`, 'red'));
      throw error;
    }
  }

  /**
   * Generate detailed test report
   */
  generateReport() {
    const totalDuration = this.results.endTime - this.results.startTime;
    const avgDuration = this.results.timings.length > 0 ? 
      Math.round(this.results.timings.reduce((a, b) => a + b, 0) / this.results.timings.length) : 0;
    const minDuration = this.results.timings.length > 0 ? Math.min(...this.results.timings) : 0;
    const maxDuration = this.results.timings.length > 0 ? Math.max(...this.results.timings) : 0;
    const successRate = Math.round((this.results.successful / this.results.total) * 100);
    const throughput = Math.round((this.results.successful / (totalDuration / 1000)) * 100) / 100;
    
    console.log(colorize('\n📊 LOAD TEST RESULTS', 'cyan'));
    console.log('='.repeat(80));
    console.log(colorize(`📈 Summary:`, 'bright'));
    console.log(`   Total Users: ${this.results.total}`);
    console.log(`   Successful: ${colorize(this.results.successful, 'green')}`);
    console.log(`   Failed: ${colorize(this.results.failed, 'red')}`);
    console.log(`   Success Rate: ${colorize(successRate + '%', successRate >= 95 ? 'green' : successRate >= 80 ? 'yellow' : 'red')}`);
    console.log('');
    
    console.log(colorize(`⏱️  Performance:`, 'bright'));
    console.log(`   Total Duration: ${Math.round(totalDuration / 1000)}s`);
    console.log(`   Average Response Time: ${avgDuration}ms`);
    console.log(`   Min Response Time: ${minDuration}ms`);
    console.log(`   Max Response Time: ${maxDuration}ms`);
    console.log(`   Throughput: ${throughput} requests/second`);
    console.log('');
    
    if (this.results.errors.length > 0) {
      console.log(colorize(`❌ Errors (showing first 10):`, 'red'));
      this.results.errors.slice(0, 10).forEach(error => {
        console.log(`   User ${error.userIndex}: ${error.error}`);
      });
      
      if (this.results.errors.length > 10) {
        console.log(`   ... and ${this.results.errors.length - 10} more errors`);
      }
      console.log('');
    }
    
    // Performance analysis
    console.log(colorize(`🎯 Performance Analysis:`, 'bright'));
    if (successRate >= 95) {
      console.log(colorize(`   ✅ Excellent: ${successRate}% success rate`, 'green'));
    } else if (successRate >= 80) {
      console.log(colorize(`   ⚠️  Good: ${successRate}% success rate`, 'yellow'));
    } else {
      console.log(colorize(`   ❌ Poor: ${successRate}% success rate`, 'red'));
    }
    
    if (avgDuration <= 30000) {
      console.log(colorize(`   ✅ Good response time: ${avgDuration}ms average`, 'green'));
    } else if (avgDuration <= 60000) {
      console.log(colorize(`   ⚠️  Acceptable response time: ${avgDuration}ms average`, 'yellow'));
    } else {
      console.log(colorize(`   ❌ Slow response time: ${avgDuration}ms average`, 'red'));
    }
    
    if (throughput >= 5) {
      console.log(colorize(`   ✅ Good throughput: ${throughput} req/s`, 'green'));
    } else if (throughput >= 2) {
      console.log(colorize(`   ⚠️  Moderate throughput: ${throughput} req/s`, 'yellow'));
    } else {
      console.log(colorize(`   ❌ Low throughput: ${throughput} req/s`, 'red'));
    }
    
    console.log('='.repeat(80));
    
    // Save detailed results to file
    this.saveResultsToFile();
  }

  /**
   * Save results to JSON file
   */
  saveResultsToFile() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `load-test-results-${timestamp}.json`;
    const filepath = path.join(__dirname, 'test-results', filename);
    
    // Create test-results directory if it doesn't exist
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    const reportData = {
      ...this.results,
      summary: {
        totalDuration: this.results.endTime - this.results.startTime,
        avgDuration: this.results.timings.length > 0 ? 
          Math.round(this.results.timings.reduce((a, b) => a + b, 0) / this.results.timings.length) : 0,
        successRate: Math.round((this.results.successful / this.results.total) * 100),
        throughput: Math.round((this.results.successful / ((this.results.endTime - this.results.startTime) / 1000)) * 100) / 100
      }
    };
    
    fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
    console.log(colorize(`💾 Detailed results saved to: ${filename}`, 'blue'));
  }
}

async function main() {
  const args = process.argv.slice(2);
  const totalUsers = parseInt(args[0]) || 500;
  const concurrency = parseInt(args[1]) || 50;
  
  console.log(colorize('🧪 ATMA Load Test Runner', 'bright'));
  console.log(`Usage: node load-test.js [totalUsers] [concurrency]`);
  console.log(`Running with: ${totalUsers} users, ${concurrency} concurrent\n`);
  
  const loadTest = new LoadTestRunner();
  
  try {
    await loadTest.runLoadTest(totalUsers, concurrency);
    console.log(colorize('\n🎉 Load test completed successfully!', 'green'));
  } catch (error) {
    console.log(colorize(`\n💥 Load test failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log(colorize('\n\n⚠️ Load test interrupted by user', 'yellow'));
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = LoadTestRunner;
