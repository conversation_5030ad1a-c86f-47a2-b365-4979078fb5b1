#!/usr/bin/env node

/**
 * ATMA Load Test - 500 Concurrent Users
 * Script untuk melakukan load testing dengan 500 user bersamaan
 */

const TestHelpers = require('./utils/test-helpers');
const pLimit = require('p-limit');
const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Load environment configuration
require('dotenv').config();

// Global test configuration
global.testConfig = {
  authServiceUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
  assessmentServiceUrl: process.env.ASSESSMENT_SERVICE_URL || 'http://localhost:3003',
  archiveServiceUrl: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002',
  apiGatewayUrl: process.env.API_GATEWAY_URL || 'http://localhost:3000',
  notificationServiceUrl: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3005',
  testPassword: process.env.TEST_PASSWORD || 'testpass123',
  serviceApiKey: process.env.SERVICE_API_KEY || 'test-service-key'
};

// Global helper functions
global.generateTestEmail = () => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `test-${timestamp}-${random}@example.com`;
};

global.sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

class LoadTestRunner {
  constructor() {
    this.testHelpers = new TestHelpers();
    this.results = {
      total: 0,
      successful: 0,
      failed: 0,
      errors: [],
      timings: [],
      startTime: null,
      endTime: null
    };
  }

  /**
   * Run single user test scenario
   */
  async runSingleUserTest(userIndex) {
    const startTime = Date.now();
    const testId = `user-${userIndex}`;
    const timings = {};

    try {
      // 1. Register user
      const regStart = Date.now();
      const testUser = await this.testHelpers.registerUser();
      timings.register = Date.now() - regStart;
      console.log(`${testId} register ${timings.register}ms`);

      // 2. Login user
      const loginStart = Date.now();
      const loginResult = await this.testHelpers.loginUser(testUser.email, testUser.password);
      const authToken = loginResult.token;
      timings.login = Date.now() - loginStart;
      console.log(`${testId} login ${timings.login}ms`);

      // 3. Submit assessment
      const submitStart = Date.now();
      const assessmentData = this.testHelpers.loadSampleAssessment();
      const submitResult = await this.testHelpers.submitAssessment(authToken, assessmentData);
      const jobId = submitResult.data.jobId;
      timings.submit = Date.now() - submitStart;
      console.log(`${testId} submit ${timings.submit}ms`);

      // 4. Wait for completion
      const processStart = Date.now();
      await this.testHelpers.waitForAssessmentCompletion(
        authToken,
        jobId,
        100000 // 100 seconds timeout
      );
      timings.process = Date.now() - processStart;
      console.log(`${testId} process ${timings.process}ms`);

      // 5. Verify results in archive
      const verifyStart = Date.now();
      await global.sleep(2000);
      const archiveResults = await this.testHelpers.getAnalysisResults(authToken);
      timings.verify = Date.now() - verifyStart;

      const totalDuration = Date.now() - startTime;
      console.log(colorize(`${testId} SELESAI total ${totalDuration}ms`, 'green'));

      return {
        success: true,
        userIndex,
        testId,
        duration: totalDuration,
        timings,
        archetype: archiveResults.data.length > 0 ? archiveResults.data[0].persona_profile.archetype : 'unknown'
      };

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      console.log(colorize(`${testId} GAGAL ${error.message} (${totalDuration}ms)`, 'red'));

      return {
        success: false,
        userIndex,
        testId,
        error: error.message,
        duration: totalDuration,
        timings
      };
    }
  }

  /**
   * Run load test with specified number of concurrent users
   */
  async runLoadTest(totalUsers = 500, concurrency = 50) {
    console.log(colorize('\n🧪 ATMA LOAD TEST - 500 CONCURRENT USERS', 'cyan'));
    console.log('='.repeat(80));
    console.log(colorize(`👥 Total Users: ${totalUsers}`, 'yellow'));
    console.log(colorize(`⚡ Concurrency: ${concurrency}`, 'yellow'));
    console.log(colorize(`🎯 Target: Full assessment flow per user`, 'yellow'));
    console.log('='.repeat(80));
    
    // Check service health first
    console.log(colorize('\n🏥 Checking service health...', 'blue'));
    try {
      await this.testHelpers.checkAllServicesHealth();
      console.log(colorize('✅ All services are healthy', 'green'));
    } catch (error) {
      console.log(colorize('❌ Some services are not healthy. Continuing anyway...', 'yellow'));
    }
    
    this.results.startTime = Date.now();
    this.results.total = totalUsers;
    
    // Create concurrency limiter
    const limit = pLimit(concurrency);
    
    // Create array of user test promises
    const userTests = Array.from({ length: totalUsers }, (_, index) => 
      limit(() => this.runSingleUserTest(index + 1))
    );
    
    console.log(colorize(`\n🚀 Starting ${totalUsers} user tests with ${concurrency} concurrent...`, 'blue'));

    // Progress tracking
    let completed = 0;
    const progressInterval = setInterval(() => {
      const progress = Math.round((completed / totalUsers) * 100);
      console.log(`Progress: ${completed}/${totalUsers} (${progress}%) - Success: ${this.results.successful}, Failed: ${this.results.failed}`);
    }, 10000);
    
    // Execute all tests
    try {
      const results = await Promise.allSettled(userTests);
      
      clearInterval(progressInterval);
      this.results.endTime = Date.now();
      
      // Process results
      const stepStats = {
        register: { success: 0, total: 0, totalTime: 0 },
        login: { success: 0, total: 0, totalTime: 0 },
        submit: { success: 0, total: 0, totalTime: 0 },
        process: { success: 0, total: 0, totalTime: 0 },
        verify: { success: 0, total: 0, totalTime: 0 }
      };

      results.forEach((result, index) => {
        completed++;

        if (result.status === 'fulfilled' && result.value.success) {
          this.results.successful++;
          this.results.timings.push(result.value.duration);

          // Collect step statistics
          const timings = result.value.timings;
          Object.keys(stepStats).forEach(step => {
            if (timings[step]) {
              stepStats[step].success++;
              stepStats[step].totalTime += timings[step];
            }
            stepStats[step].total++;
          });
        } else {
          this.results.failed++;
          const error = result.status === 'rejected' ? result.reason.message : result.value.error;
          this.results.errors.push({
            userIndex: index + 1,
            error: error
          });

          // Count failed steps
          Object.keys(stepStats).forEach(step => {
            stepStats[step].total++;
          });
        }
      });

      this.results.stepStats = stepStats;
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      clearInterval(progressInterval);
      console.log(colorize(`💥 Load test failed: ${error.message}`, 'red'));
      throw error;
    }
  }

  /**
   * Generate simple test report
   */
  generateReport() {
    const totalDuration = this.results.endTime - this.results.startTime;
    const avgDuration = this.results.timings.length > 0 ?
      Math.round(this.results.timings.reduce((a, b) => a + b, 0) / this.results.timings.length) : 0;
    const successRate = Math.round((this.results.successful / this.results.total) * 100);
    const throughput = Math.round((this.results.successful / (totalDuration / 1000)) * 100) / 100;

    console.log(colorize('\n📊 HASIL LOAD TEST', 'cyan'));
    console.log('='.repeat(60));

    // Summary
    console.log(`Total Users: ${this.results.total}`);
    console.log(`Berhasil: ${this.results.successful} (${successRate}%)`);
    console.log(`Gagal: ${this.results.failed}`);
    console.log(`Durasi Total: ${Math.round(totalDuration / 1000)}s`);
    console.log(`Rata-rata Full Journey: ${avgDuration}ms per user`);
    console.log(`Throughput: ${throughput} user/detik`);
    console.log('');

    // Step analysis
    if (this.results.stepStats) {
      console.log('📈 ANALISIS PER STEP:');
      Object.entries(this.results.stepStats).forEach(([step, stats]) => {
        const stepSuccessRate = Math.round((stats.success / stats.total) * 100);
        const avgStepTime = stats.success > 0 ? Math.round(stats.totalTime / stats.success) : 0;
        console.log(`${step.padEnd(10)}: ${stepSuccessRate}% berhasil, rata-rata ${avgStepTime}ms`);
      });
      console.log('');
    }

    // Bottleneck analysis
    console.log('🔍 ANALISIS BOTTLENECK:');
    if (this.results.stepStats) {
      const stepAvgs = {};
      Object.entries(this.results.stepStats).forEach(([step, stats]) => {
        stepAvgs[step] = stats.success > 0 ? stats.totalTime / stats.success : 0;
      });

      const sortedSteps = Object.entries(stepAvgs)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);

      sortedSteps.forEach(([step, avgTime], index) => {
        const emoji = index === 0 ? '🐌' : index === 1 ? '⚠️' : '📊';
        console.log(`${emoji} ${step}: ${Math.round(avgTime)}ms rata-rata`);
      });
    }
    console.log('');

    // Simple conclusion
    console.log('🎯 KESIMPULAN:');
    if (successRate >= 95) {
      console.log(colorize('✅ Sistem stabil untuk load ini', 'green'));
    } else if (successRate >= 80) {
      console.log(colorize('⚠️ Sistem cukup stabil, ada room for improvement', 'yellow'));
    } else {
      console.log(colorize('❌ Sistem tidak stabil untuk load ini', 'red'));
    }

    if (avgDuration <= 30000) {
      console.log(colorize('✅ Response time bagus', 'green'));
    } else if (avgDuration <= 60000) {
      console.log(colorize('⚠️ Response time acceptable', 'yellow'));
    } else {
      console.log(colorize('❌ Response time terlalu lambat', 'red'));
    }

    console.log('='.repeat(60));

    // Save detailed results to file
    this.saveResultsToFile();
  }

  /**
   * Save results to JSON file
   */
  saveResultsToFile() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `load-test-results-${timestamp}.json`;
    const filepath = path.join(__dirname, 'test-results', filename);
    
    // Create test-results directory if it doesn't exist
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    const reportData = {
      ...this.results,
      summary: {
        totalDuration: this.results.endTime - this.results.startTime,
        avgDuration: this.results.timings.length > 0 ? 
          Math.round(this.results.timings.reduce((a, b) => a + b, 0) / this.results.timings.length) : 0,
        successRate: Math.round((this.results.successful / this.results.total) * 100),
        throughput: Math.round((this.results.successful / ((this.results.endTime - this.results.startTime) / 1000)) * 100) / 100
      }
    };
    
    fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
    console.log(colorize(`💾 Detailed results saved to: ${filename}`, 'blue'));
  }
}

async function main() {
  const args = process.argv.slice(2);
  const totalUsers = parseInt(args[0]) || 500;
  const concurrency = parseInt(args[1]) || 50;
  
  console.log(colorize('🧪 ATMA Load Test Runner', 'bright'));
  console.log(`Usage: node load-test.js [totalUsers] [concurrency]`);
  console.log(`Running with: ${totalUsers} users, ${concurrency} concurrent\n`);
  
  const loadTest = new LoadTestRunner();
  
  try {
    await loadTest.runLoadTest(totalUsers, concurrency);
    console.log(colorize('\n🎉 Load test completed successfully!', 'green'));
  } catch (error) {
    console.log(colorize(`\n💥 Load test failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log(colorize('\n\n⚠️ Load test interrupted by user', 'yellow'));
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = LoadTestRunner;
